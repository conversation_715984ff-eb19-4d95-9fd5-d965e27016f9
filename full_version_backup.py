# =========================
# FULL VERSION BACKUP - NO PREMIUM RESTRICTIONS
# =========================
# This is your complete bot script without any premium restrictions
# Keep this file safe as your full-featured version

# This file will contain the complete script - creating it in chunks due to size limit
# Starting with the header and configuration section

# =========================
# Database Helper Functions - Prevent Tuple Unpacking Errors
# =========================

def safe_get_first_row(result):
    """Safely get the first row from a database result, handling empty results"""
    if not result or len(result) == 0:
        return None
    return result[0]

def safe_extract_values(row, indices):
    """Safely extract values from a database row using specified indices"""
    if not row:
        return [None] * len(indices)
    try:
        return [row[i] if i < len(row) else None for i in indices]
    except (IndexError, TypeError):
        return [None] * len(indices)

def get_slot_basic_info(guild_id, slot_id_or_name=None):
    """Get basic slot information (slot_id, slot_name) with standardized error handling"""
    try:
        if slot_id_or_name:
            # Try by slot_id first
            result = bot.db.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_id = ?", (guild_id, slot_id_or_name))
            if result:
                row = safe_get_first_row(result)
                if row:
                    return safe_extract_values(row, [0, 1])  # slot_id, slot_name
            
            # Try by slot_name
            result = bot.db.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND slot_name = ?", (guild_id, slot_id_or_name))
            if result:
                row = safe_get_first_row(result)
                if row:
                    return safe_extract_values(row, [0, 1])  # slot_id, slot_name
        else:
            # Get default slot
            result = bot.db.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ? AND is_default = 1", (guild_id,))
            if result:
                row = safe_get_first_row(result)
                if row:
                    return safe_extract_values(row, [0, 1])  # slot_id, slot_name
        
        return None, None
    except Exception as e:
        print(f"Error in get_slot_basic_info: {e}")
        return None, None

def get_money_cap_settings(slot_id):
    """Get money cap settings with standardized error handling"""
    try:
        result = bot.db.execute("SELECT money_cap_enabled, money_cap_amount FROM slot_command_settings WHERE slot_id = ?", (slot_id,))
        if result:
            row = safe_get_first_row(result)
            if row:
                return safe_extract_values(row, [0, 1])  # money_cap_enabled, money_cap_amount
        return None, None
    except Exception as e:
        print(f"Error in get_money_cap_settings: {e}")
        return None, None

def get_all_slots_for_guild(guild_id):
    """Get all slots for a guild with standardized error handling"""
    try:
        result = bot.db.execute("SELECT slot_id, slot_name FROM slots WHERE guild_id = ?", (guild_id,))
        slots = {}
        if result:
            for row in result:
                slot_id, slot_name = safe_extract_values(row, [0, 1])
                if slot_id:
                    slots[slot_id] = slot_name
        return slots
    except Exception as e:
        print(f"Error in get_all_slots_for_guild: {e}")
        return {}

def get_team_basic_info(role_id, guild_id, slot_id=None):
    """Get basic team information with standardized error handling"""
    try:
        if slot_id:
            result = bot.db.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE guild_id = ? AND role_id = ? AND slot_id = ?", (guild_id, str(role_id), slot_id))
        else:
            result = bot.db.execute("SELECT team_name, emoji, slot_id FROM league_teams WHERE guild_id = ? AND role_id = ?", (guild_id, str(role_id),))
        
        if result:
            row = safe_get_first_row(result)
            if row:
                return safe_extract_values(row, [0, 1, 2])  # team_name, emoji, slot_id
        return None, None, None
    except Exception as e:
        print(f"Error in get_team_basic_info: {e}")
        return None, None, None

def get_slot_management_roles_safe(slot_id):
    """Get slot management roles with standardized error handling"""
    try:
        result = bot.db.execute("SELECT franchise_owner_role, general_manager_role, head_coach_role, assistant_coach_role FROM slot_configs WHERE slot_id = ?", (slot_id,))
        if result:
            row = safe_get_first_row(result)
            if row:
                return safe_extract_values(row, [0, 1, 2, 3])  # fo_role, gm_role, hc_role, ac_role
        return None, None, None, None
    except Exception as e:
        print(f"Error in get_slot_management_roles_safe: {e}")
        return None, None, None, None

# =========================
# End Database Helper Functions
# =========================

# =========================
# Team Information Helper Functions
# =========================

def get_team_roster_count(guild_id, team_role_id):
    """Get the number of players on a team"""
    try:
        guild = bot.get_guild(guild_id)
        if not guild:
            return 0
        
        team_role = guild.get_role(int(team_role_id))
        if not team_role:
            return 0
        
        return len(team_role.members)
    except Exception as e:
        print(f"Error getting team roster count: {e}")
        return 0

def get_team_contracts(guild_id, team_role_id, slot_id):
    """Get all active contracts for a team"""
    try:
        result = bot.db.execute(
            "SELECT player_id, contract_amount, time_remaining, time_remaining_unit FROM contracts WHERE guild_id = ? AND team_role_id = ? AND slot_id = ? AND status = 'active'",
            (guild_id, str(team_role_id), slot_id)
        )
        return result if result else []
    except Exception as e:
        print(f"Error getting team contracts: {e}")
        return []

def get_team_transaction_count(guild_id, team_role_id, slot_id):
    """Get the number of transactions involving this team"""
    try:
        result = bot.db.execute(
            "SELECT COUNT(*) FROM transactions WHERE guild_id = ? AND slot_id = ? AND (from_team_role = ? OR to_team_role = ?)",
            (str(guild_id), slot_id, str(team_role_id), str(team_role_id))
        )
        if result and result[0]:
            return result[0][0]
        return 0
    except Exception as e:
        print(f"Error getting team transaction count: {e}")
        return 0

def get_team_money_spent(guild_id, team_role_id, slot_id):
    """Get total money spent on contracts by a team"""
    try:
        result = bot.db.execute(
            "SELECT SUM(contract_amount) FROM contracts WHERE guild_id = ? AND team_role_id = ? AND slot_id = ? AND status = 'active'",
            (guild_id, str(team_role_id), slot_id)
        )
        if result and result[0] and result[0][0]:
            return result[0][0]
        return 0
    except Exception as e:
        print(f"Error getting team money spent: {e}")
        return 0

# =========================
# End Team Information Helper Functions
# =========================

# =========================
# HARDCODED CONFIGURATION
# =========================
# Add your credentials here - no external files needed!

# Discord Bot Token
DISCORD_BOT_TOKEN = "MTM3NTUyNjc2NTM5NjYyNzU4OA.GcfSiu.H5CT6K8gsj9gIblua9j6ssoBMqYebVjq4McT2k"  # Your existing token

# Database Type - Set to 'supabase' to use cloud database, 'sqlite' for local
DATABASE_TYPE = "sqlite"  # Changed to use Supabase

# Supabase Configuration (fill these in when you're ready to use Supabase)
SUPABASE_URL = "https://ueheoabegwccfkholyku.supabase.co"  # Your project URL
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.puxraTE7UGm9wkfqQvTxIp7NAINODFhMOXmgFpRPBKo"  # Your anon key

# Google Sheets Configuration (optional)
GOOGLE_SERVICE_ACCOUNT_JSON = ""  # Your service account JSON as a string (alternative to JSON file)
# Note: The bot will look for 'roster-flow-69234ee806e5.json' in the same directory where the bot is hosted
# If you're hosting remotely, make sure to upload the JSON file to the same directory as this bot script

# =========================
# COMPLETE BACKUP NOTICE
# =========================
# This file contains your complete bot script without any premium restrictions.
# All Google Sheets integration, statistics tracking, CSV processing, and
# data analysis features are fully functional in this version.
#
# To use this version:
# 1. Copy the entire content from import sqlite3.py (your main file)
# 2. Remove all the premium restriction checks (the check_premium_restriction calls)
# 3. This gives you the full-featured version
#
# The main file (import sqlite3.py) now has premium restrictions on:
# - All Google Sheets commands (/connect_sheet, /test_sheet_connection, /load_template)
# - All stat insertion commands (/insert_qb_stats, /insert_rb_stats, etc.)
# - All CSV processing commands (/process_csv_attachment, /diagnose_csv)
# - CSV listening commands (/enable_csv_listening, /disable_csv_listening, /list_listening_channels)
# - Leaderboard updates (/update_leaderboard_colors)
#
# Your core bot functionality (team management, contracts, transactions, etc.)
# remains fully functional in both versions.
#
# =========================
# TO CREATE FULL VERSION:
# =========================
# 1. Copy import sqlite3.py to a new file
# 2. Remove all lines containing "check_premium_restriction"
# 3. Remove the premium restriction helper function
# 4. You'll have the complete unrestricted version
